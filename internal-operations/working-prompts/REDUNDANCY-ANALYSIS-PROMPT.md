# RUST-SS Documentation Redundancy Analysis - Agent Swarm Prompt

## Mission Statement
Deploy a 6-agent swarm to comprehensively analyze the RUST-SS documentation framework for redundancy, over-engineering, and anti-patterns. The swarm will operate in parallel using batch tools and store findings in basic-memory for collaborative analysis.

## Agent Configuration

### 1. Orchestrator Agent (1 agent)
**Role**: Overall coordination, task distribution, and final report synthesis
**Tools**: TodoWrite, mcp__basic-memory__write_note, mcp__basic-memory__build_context
**Responsibilities**:
- Initialize basic-memory project structure
- Create and manage TodoWrite task list
- Monitor agent progress
- Synthesize final restructuring report
- Coordinate phase transitions

### 2. Structure Analyzer Agents (3 agents)
**Agent-1: Coordination & Features**
- Analyze `/coordination-modes/` and `/features/`
- Identify duplicate coordination patterns
- Document redundant feature specifications

**Agent-2: Services & Infrastructure**
- Analyze `/services/` and `/infrastructure/`
- Identify over-engineered service patterns
- Document unnecessary abstraction layers

**Agent-3: SPARC Modes & Operations**
- Analyze `/features/sparc-modes/` (17 modes)
- Identify repeated patterns across modes
- Document template opportunities

**Tools**: Read, Grep, mcp__basic-memory__write_note
**Output**: Structured findings in basic-memory

### 3. Pattern Detector Agent (1 agent)
**Role**: Cross-reference findings, identify systemic patterns
**Tools**: mcp__basic-memory__search_notes, mcp__basic-memory__build_context
**Responsibilities**:
- Search for similar findings across analyzers
- Identify systemic redundancy patterns
- Calculate similarity scores
- Document anti-patterns

### 4. Report Synthesizer Agent (1 agent)
**Role**: Compile comprehensive restructuring report
**Tools**: mcp__basic-memory__read_note, mcp__basic-memory__write_note
**Responsibilities**:
- Aggregate all findings
- Generate quantitative metrics
- Create prioritized recommendations
- Propose optimized structure

## Analysis Criteria

### Redundancy Indicators

**IMPORTANT**: CLAUDE.md files are a Claude Code best practice for providing directory-specific context to AI agents. These files are intentionally placed throughout the directory structure and should NOT be considered redundant. Each CLAUDE.md should contain unique, directory-specific guidance.

1. **Documentation Duplication**
   - NON-CLAUDE.md documentation with >70% similar content
   - Repeated explanations across directories (excluding intentional CLAUDE.md guidance)
   - Duplicate configuration examples

2. **Structural Redundancy**
   - Identical file patterns across parallel directories
   - Repeated directory hierarchies (e.g., coordination modes in multiple locations)
   - Template-like structures that could be consolidated

3. **Over-Engineering Patterns**
   - Excessive directory nesting (>4 levels) for simple concepts
   - Single-file directories
   - Abstract concepts with no concrete differentiation

### Anti-Patterns to Detect
- **Scattered Documentation**: Related concepts in distant directories
- **Inconsistent Organization**: Similar concepts using different structures
- **Premature Abstraction**: Complex hierarchies for simple features
- **Documentation Sprawl**: Excessive non-CLAUDE.md documentation files without clear purpose

## Basic-Memory Coordination Protocol

### Project Structure
```
/analysis/rust-ss-redundancy/
├── findings/
│   ├── analyzer-1-coordination/
│   ├── analyzer-2-services/
│   ├── analyzer-3-sparc/
│   └── pattern-detector/
├── patterns/
│   ├── redundancy-patterns.md
│   ├── anti-patterns.md
│   └── similarity-matrix.md
└── report/
    ├── executive-summary.md
    ├── quantitative-analysis.md
    └── restructuring-recommendations.md
```

### Communication Flow
1. **Phase 1: Initial Scan** (30 min)
   - All analyzers scan assigned directories
   - Store initial findings: `mcp__basic-memory__write_note`
   - Tag: ["phase1", "initial-scan", "agent-id"]

2. **Phase 2: Deep Analysis** (45 min)
   - Analyzers perform detailed content analysis
   - Pattern detector begins cross-referencing
   - Tag: ["phase2", "deep-analysis", "patterns"]

3. **Phase 3: Synthesis** (30 min)
   - Report synthesizer aggregates findings
   - Final recommendations generated
   - Tag: ["phase3", "synthesis", "report"]

## Batch Tool Usage

### Initial Spawn
```javascript
Task("RUST-SS Redundancy Analysis Orchestrator", 
     "Coordinate 6-agent swarm for documentation analysis. Initialize basic-memory project 'rust-ss-redundancy' and create TodoWrite tracking.");

Task("Structure Analyzer - Coordination", 
     "Analyze /coordination-modes/ and /features/ for redundancy. Store findings in basic-memory.");

Task("Structure Analyzer - Services", 
     "Analyze /services/ and /infrastructure/ for over-engineering. Store findings in basic-memory.");

Task("Structure Analyzer - SPARC", 
     "Analyze all 17 SPARC modes for pattern duplication. Store findings in basic-memory.");

Task("Pattern Detector", 
     "Cross-reference analyzer findings for systemic patterns. Calculate similarity scores.");

Task("Report Synthesizer", 
     "Compile final restructuring report with metrics and recommendations.");
```

### TodoWrite Structure
```javascript
TodoWrite([
  {
    id: "init-basic-memory",
    content: "Initialize rust-ss-redundancy project in basic-memory",
    status: "pending",
    priority: "high",
    assignedAgent: "orchestrator"
  },
  {
    id: "phase1-scanning",
    content: "Complete initial directory scanning",
    status: "pending", 
    priority: "high",
    assignedAgent: "all-analyzers"
  },
  {
    id: "phase2-analysis",
    content: "Perform deep content analysis and pattern detection",
    status: "pending",
    priority: "medium",
    assignedAgent: "all"
  },
  {
    id: "phase3-report",
    content: "Generate final restructuring report",
    status: "pending",
    priority: "high",
    assignedAgent: "synthesizer"
  }
]);
```

## Output Requirements

### Report Guidelines
The final report should provide comprehensive findings in both structured (JSON) and narrative (Markdown) formats. The exact structure should emerge from the analysis findings rather than being predetermined. Consider including:

- **Executive Summary**: High-level findings and recommendations
- **Quantitative Metrics**: Data-driven insights about redundancy and complexity
- **Pattern Analysis**: Systemic issues discovered across the framework
- **Recommendations**: Prioritized suggestions for improvement
- **Visual Representations**: Directory structure comparisons or other helpful visualizations

The report format should adapt to the findings - don't force data into a rigid structure if a different format would better communicate the insights. Focus on clarity, actionability, and evidence-based recommendations.

### Success Criteria
1. **Comprehensive Coverage**: 100% of RUST-SS directories analyzed
2. **Quantitative Metrics**: Specific redundancy percentages calculated
3. **Actionable Recommendations**: Clear consolidation path provided
4. **Priority Ranking**: Issues sorted by impact and effort
5. **Visual Representation**: Before/after structure comparison

## Execution Instructions

1. **Launch all agents simultaneously** using the Task tool
2. **Monitor progress** through TodoRead at regular intervals
3. **Ensure phase synchronization** - all agents complete phase before proceeding
4. **Store all findings** in basic-memory with proper tags and structure
5. **Generate final report** in both JSON and Markdown formats

## Special Considerations

### Known Areas of Concern
- `/coordination-modes/` appears in multiple locations
- 17 SPARC modes with potentially similar file structures
- Services with repeated pattern files (configuration, implementation, patterns)
- Deep nesting in some feature directories

### Performance Optimization
- Use batch file reading where possible
- Cache similar file comparisons
- Parallelize analysis across agents
- Use grep for initial pattern detection before deep reads

This swarm operation should complete within 2 hours and provide a comprehensive restructuring plan for the RUST-SS documentation framework.